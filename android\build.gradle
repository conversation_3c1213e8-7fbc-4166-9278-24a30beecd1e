// In android/build.gradle

buildscript {
    ext.kotlin_version = '1.7.10'
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.3.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        // This line makes the Google Services plugin available to the project
        classpath 'com.google.gms:google-services:4.4.0'
    }
}

// ===================================================================
// <<< --- THIS IS THE FIX FOR PLUGINS LIKE 'app_links' --- >>>
// This creates the global 'flutter' object that plugins expect to exist.
ext.flutter = [
    compileSdkVersion: 34,
    minSdkVersion:     21,
    targetSdkVersion:  34,
    ndkVersion:        "25.1.8937393"
]
// ===================================================================

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = '../build'

subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}