// In android/app/build.gradle

// The google-services plugin is REMOVED from this block.
plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    namespace = "com.example.harmonizr360" // <<< ACTION: Make sure this is your actual package name

    // Uses the SDK versions from the global 'flutter' object defined in the other file
    compileSdkVersion flutter.compileSdkVersion
    ndkVersion flutter.ndkVersion

    // Fixes the Java/Kotlin version mismatch
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId = "com.example.harmonizr360" // <<< ACTION: Make sure this is your actual package name
        minSdkVersion flutter.minSdkVersion
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // signingConfig signingConfigs.release
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    // Import the Firebase BoM (Bill of Materials)
    implementation platform('com.google.firebase:firebase-bom:33.1.0')

    // Add your Firebase dependencies
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-messaging'
    
    // Add any other dependencies here
}

// ===================================================================
// <<< --- THIS IS THE FIX FOR THE 'google-services plugin not found' ERROR --- >>>
// This applies the plugin at the end of the file, which works with the classpath definition.
apply plugin: 'com.google.gms.google-services'
// ===================================================================